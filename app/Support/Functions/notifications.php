<?php

use App\Models\User;
use Filament\Notifications\Notification;
use Illuminate\Database\Eloquent\Builder;

function success_notification(string $body, bool $persistent = false): ?Notification
{
    $notification = Notification::make()
        ->title('Sucesso!')
        ->body($body)
        ->success();

    if ($persistent) {
        $notification->persistent();
    } else {
        $notification->seconds(5);
    }

    return $notification;
}

function warning_notification(string $body, bool $persistent = false): ?Notification
{
    $notification = Notification::make()
        ->title('Atenção!')
        ->body($body)
        ->warning();

    if ($persistent) {
        $notification->persistent();
    } else {
        $notification->seconds(5);
    }

    return $notification;
}

function error_notification(?string $body = null, bool $persistent = true): ?Notification
{
    $notification = Notification::make()
        ->title('Ops!')
        ->body($body ?? __('core.errors.general'))
        ->danger();

    if ($persistent) {
        $notification->persistent();
    } else {
        $notification->seconds(5);
    }

    return $notification;
}

function success_database_notification(?int $userId = null, ?string $body = null, bool $onlyCreationUser = false): void
{
    User::query()
        ->when($userId, fn (Builder $query): Builder => $query->where('id', $userId))
        ->when(!$onlyCreationUser, function (Builder $query): Builder {
            return $query->orWhere(fn (Builder $query): Builder => $query->role('Administrador'));
        })
        ->get()
        ->each(function (User $user) use ($body): void {
            $notification = success_notification($body)->toDatabase();
            $user->notify($notification);
        });
}

function warning_database_notification(?int $userId = null, ?string $body = null, bool $onlyCreationUser = false): void
{
    User::query()
        ->when($userId, fn (Builder $query): Builder => $query->where('id', $userId))
        ->when(!$onlyCreationUser, function (Builder $query): Builder {
            return $query->orWhere(fn (Builder $query): Builder => $query->role('Administrador'));
        })
        ->get()
        ->each(function (User $user) use ($body): void {
            $notification = warning_notification($body)->toDatabase();
            $user->notify($notification);
        });
}

function error_database_notification(?int $userId = null, ?string $body = null, bool $onlyCreationUser = false): void
{
    User::query()
        ->when($userId, fn (Builder $query): Builder => $query->where('id', $userId))
        ->when(!$onlyCreationUser, function (Builder $query): Builder {
            return $query->orWhere(fn (Builder $query): Builder => $query->role('Administrador'));
        })
        ->get()
        ->each(function (User $user) use ($body): void {
            $notification = error_notification($body)->toDatabase();
            $user->notify($notification);
        });
}
