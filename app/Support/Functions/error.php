<?php

/*
|--------------------------------------------------------------------------
| Error helpers
|--------------------------------------------------------------------------
|
*/

if (!function_exists('error')) {
    function error($message, $context = [])
    {
        app('log')->error($message, $context);
    }
}

function throw_error(\Throwable $th, ?string $message = null, bool $log = true): void
{
    if ($log) {
        error($th);
    }

    throw new \Exception($message ?? __('general.unknown_error'));
}

function validate_foreign_key(Throwable $th, string $table, string $entity, string $reason)
{
    if ((int) $th->getCode() === 23000 && str_contains($th->getMessage(), $table)) {
        throw new Exception(__("Desculpe, não foi possível excluir $entity pois $reason."));
    }
}
